[tool.poetry]
name = "nie-api"
version = "0.1.0"
description = "NIE API"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "nie_api"}]

[tool.poetry.dependencies]
python = "^3.10"
flask = "^3.0.0"
flask-restful = "^0.3.10"
flask-cors = "^4.0.0"
flask-sqlalchemy = "^3.1.1"
flask-migrate = "^4.0.5"
python-dotenv = "^1.0.0"
psycopg2-binary = "^2.9.9"
gunicorn = "^21.2.0"
gevent = "^23.9.1"
pyjwt = "^2.8.0"
requests = "^2.31.0"
pydantic = "^2.9.2"
redis = "^5.0.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
black = "^23.0.0"
flake8 = "^6.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"