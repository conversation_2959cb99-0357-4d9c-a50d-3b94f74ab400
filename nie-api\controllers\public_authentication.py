import os
import uuid
import logging
import traceback
from datetime import UTC, datetime, timedelta, timezone
from functools import wraps

import jwt
import requests
from flask import jsonify, make_response, request
from flask_restful import Resource, reqparse
from sqlalchemy.exc import SQLAlchemyError
from werkzeug.exceptions import HTTPException, NotFound

from models.model_deeeplabs import PublicAdminAuthenticationLog, PublicUserAuthentication, PublicUserAuthenticationSettings
from models.engine import db
from services.account_service import AccountService, extract_remote_ip
from services.tenant_service import *


# Configure logging
logging.basicConfig(
    level=logging.ERROR,
    format='%(asctime)s - %(name)s - %(levelname)s - [ERROR_ID: %(error_id)s] - %(message)s',
    handlers=[
        logging.FileHandler('error.log'),
        logging.StreamHandler()  # This will print to terminal
    ]
)

logger = logging.getLogger(__name__)

NIE_API_URL = os.environ.get("NIE_API_URL")
print("NIE_API_URL: ",NIE_API_URL)

def verify_bearer_token(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        auth_header = request.headers.get("Authorization")

        if not auth_header:
            return {"message": "Authorization header is missing", "error": "Unauthorized", "status": 401}, 401

        try:
            # Extract token from "Bearer <token>"
            token_type, token = auth_header.split()
            if token_type.lower() != "bearer":
                raise ValueError("Invalid token type")

            NIE_API_TOKEN = os.environ.get("NIE_API_TOKEN")

            # Compare with configured token
            if token != NIE_API_TOKEN:
                raise ValueError("Invalid token")

        except (ValueError, AttributeError) as e:
            return {"message": "Invalid authorization token", "error": f"Unauthorized | {e}", "status": 401}, 401

        return f(*args, **kwargs)

    return decorated


class PublicAuthenticationLog(Resource):
    def __init__(self):
        # Set up SGT offset (UTC+8)
        self.SGT = timezone(timedelta(hours=8))

    """Base resource for PublicAuthentication."""

    def handle_error(self, e):
        """Enhanced error handler with UUID logging"""
        # Generate unique error ID
        error_id = str(uuid.uuid4())
        
        # Prepare error details for logging
        error_details = {
            'error_type': e.__class__.__name__,
            'error_message': str(e),
            'traceback': traceback.format_exc(),
            'request_method': request.method,
            'request_url': request.url,
            'request_args': dict(request.args),
            'user_agent': request.headers.get('User-Agent', 'Unknown'),
            'remote_addr': request.remote_addr
        }
        
        # Log error with UUID using extra parameter
        logger.error(f"============== START OF LOG for {error_id}")
        logger.error(
            f"Error occurred: {error_details['error_type']} - {error_details['error_message']}\n"
            f"Request: {error_details['request_method']} {error_details['request_url']}\n"
            f"Args: {error_details['request_args']}\n"
            f"User-Agent: {error_details['user_agent']}\n"
            f"Remote Address: {error_details['remote_addr']}\n"
            f"Traceback:\n{error_details['traceback']}",
            extra={'error_id': error_id}
        )
        logger.error(f"============== END OF LOG for {error_id}")
        
        # Determine status code and return generic message
        if isinstance(e, HTTPException):
            status_code = e.code
            # For HTTP errors, we might want to show some specific messages
            if status_code == 404:
                message = "Resource not found"
            elif status_code == 401:
                message = "Unauthorized access"
            elif status_code == 403:
                message = "Access forbidden"
            else:
                message = "An error occurred while processing your request"
        elif isinstance(e, SQLAlchemyError):
            db.session.rollback()
            status_code = 500
            message = "Database error occurred"
        else:
            status_code = 500
            message = "Internal server error"
        
        return {
            "message": message,
            "error_id": error_id,
            "status": status_code
        }, status_code
    
    def check_jsonify(self, data):
        make_response(data)
        return data

    @verify_bearer_token
    def get(self, public_user_id=None):
        """
        Read passport information.
        If user_id is provided, get specific user, otherwise list all users.
        """
        try:
            # single
            if public_user_id:
                public_user = (
                    db.session.query(PublicUserAuthentication)
                    .filter(PublicUserAuthentication.id == public_user_id)
                    .first()
                )
                if not public_user:
                    raise NotFound("User not found")

                # Convert timestamp to SGT
                sgt_timestamp = public_user.timestamp.replace(tzinfo=UTC).astimezone(self.SGT)

                return self.check_jsonify(
                    {
                        "data": {
                            "id": public_user.id,
                            "app_id": public_user.app_id,
                            "username": public_user.username,
                            "email": public_user.email,
                            "auth_type": public_user.auth_type.value,
                            "timestamp": sgt_timestamp.isoformat(),
                        },
                        "success": True,
                        "message": "User Fetched successfully",
                        "status": 200,
                    }
                ), 200

            # List all users (with pagination)
            # Get page from request and add 1 since SQLAlchemy pagination is 1-based
            page = request.args.get("page", 0, type=int) + 1  # Default to 0, add 1 for SQLAlchemy
            per_page = request.args.get("per_page", 999, type=int)
            search = request.args.get("search", "")
            app_id = request.args.get("app_id", "")

            # Get time filter as integer (days)
            days_filter = request.args.get("days_filter", 0, type=int)  # Default to 0 (all time)

            # Get sort parameter, default to "-timestamp" for descending
            sort = request.args.get("sort", "-timestamp")

            # Build query with filters
            query = db.session.query(PublicUserAuthentication)

            if app_id:
                query = query.filter(PublicUserAuthentication.app_id == app_id)

            if search:
                query = query.filter(
                    db.or_(
                        PublicUserAuthentication.username.ilike(f"%{search}%"),
                        PublicUserAuthentication.email.ilike(f"%{search}%"),
                    )
                )

            # Apply time period filter
            # Apply time period filter only if days_filter is provided and not 0
            if days_filter is not None and days_filter > 0:
                now = datetime.now(self.SGT)
                start_date = now - timedelta(days=days_filter)
                # Convert to UTC for database query
                start_date_utc = start_date.astimezone(UTC)
                query = query.filter(PublicUserAuthentication.timestamp >= start_date_utc)
            elif days_filter == 0:
                # For days_filter = 0, filter records from today only
                today_start = datetime.now(self.SGT).replace(hour=0, minute=0, second=0, microsecond=0)
                today_start_utc = today_start.astimezone(UTC)
                query = query.filter(PublicUserAuthentication.timestamp >= today_start_utc)

            # Apply sorting based on "-timestamp" notation
            if sort.startswith("-"):
                query = query.order_by(PublicUserAuthentication.timestamp.desc())
            else:
                query = query.order_by(PublicUserAuthentication.timestamp.asc())

            users = query.paginate(page=page, per_page=per_page, error_out=False)

            return self.check_jsonify(
                {
                    "data": {
                        "result": [
                            {
                                "id": user.id,
                                "app_id": user.app_id,
                                "username": user.username,
                                "email": user.email,
                                "auth_type": user.auth_type.value,
                                "timestamp": user.timestamp.replace(tzinfo=UTC)
                                .astimezone(self.SGT)
                                .strftime("%d-%m-%Y %H:%M"),
                            }
                            for user in users.items
                        ],
                        "pagination": {
                            "total_count": users.total,
                            "total_pages": users.pages,
                            "current_page": users.page - 1,  # Subtract 1 to return to zero-based indexing
                            "limit": per_page,
                            "next_page": (users.next_num - 1) if users.has_next else None,  # Subtract 1 for zero-based
                            "prev_page": (users.prev_num - 1) if users.has_prev else None,  # Subtract 1 for zero-based
                        },
                    },
                    "success": True,
                    "message": "User list Fetched Successfully",
                    "status": 200,
                }
            ), 200

        except Exception as e:
            return self.handle_error(e)

    @verify_bearer_token
    def post(self):
        """Create a new passport."""
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("email", type=str, required=True, location="json")
            parser.add_argument("auth_type", type=str, required=True, location="json")
            parser.add_argument("chat_id", type=str, required=True, location="json")
            args = parser.parse_args()

            email = args["email"]
            auth_type = args["auth_type"]
            chat_id = args["chat_id"]

            app_id = None

            if not chat_id:
                raise NotFound("App ID not found")

            site = get_site_by_code(chat_id)
            if site:
                if site.app_id:
                    app_id = str(site.app_id)
                else:
                    raise NotFound("Chat id on Site Model not found")
            else:
                raise NotFound("Site model is not found")

            if not app_id:
                raise NotFound("App ID is not found")

            # Create new end user
            public_user = PublicUserAuthentication(
                username=email.split("@")[0], email=email, auth_type=auth_type, app_id=app_id
            )

            db.session.add(public_user)
            db.session.commit()

            return self.check_jsonify(
                {
                    "data": {
                        "id": public_user.id,
                        "app_id": public_user.app_id,
                        "username": public_user.username,
                        "email": public_user.email,
                        "auth_type": public_user.auth_type.value,
                        "timestamp": public_user.timestamp.replace(tzinfo=UTC)
                        .astimezone(self.SGT)
                        .strftime("%d-%m-%Y %H:%M"),
                    },
                    "success": True,
                    "message": "User Created Successfully",
                    "status": 201,
                }
            ), 201

        except Exception as e:
            return self.handle_error(e)

    @verify_bearer_token
    def delete(self, public_user_id):
        """Delete a passport."""
        try:
            public_user = (
                db.session.query(PublicUserAuthentication).filter(PublicUserAuthentication.id == public_user_id).first()
            )
            if not public_user:
                raise NotFound("User not found")

            db.session.delete(public_user)
            db.session.commit()

            return self.check_jsonify(
                {
                    "data": {"public_user_id": public_user_id},
                    "success": True,
                    "message": "User deleted successfully",
                    "status": 200,
                }
            ), 200

        except Exception as e:
            return self.handle_error(e)


class PublicAuthenticationLogSettings(Resource):
    def __init__(self):
        # Set up SGT offset (UTC+8)
        self.SGT = timezone(timedelta(hours=8))

    """Base resource for PublicAuthentication."""

    def handle_error(self, e):
        """Enhanced error handler with UUID logging"""
        # Generate unique error ID
        error_id = str(uuid.uuid4())
        
        # Prepare error details for logging
        error_details = {
            'error_type': e.__class__.__name__,
            'error_message': str(e),
            'traceback': traceback.format_exc(),
            'request_method': request.method,
            'request_url': request.url,
            'request_args': dict(request.args),
            'user_agent': request.headers.get('User-Agent', 'Unknown'),
            'remote_addr': request.remote_addr
        }
        
        # Log error with UUID using extra parameter
        logger.error(f"============== START OF LOG for {error_id}")
        logger.error(
            f"Error occurred: {error_details['error_type']} - {error_details['error_message']}\n"
            f"Request: {error_details['request_method']} {error_details['request_url']}\n"
            f"Args: {error_details['request_args']}\n"
            f"User-Agent: {error_details['user_agent']}\n"
            f"Remote Address: {error_details['remote_addr']}\n"
            f"Traceback:\n{error_details['traceback']}",
            extra={'error_id': error_id}
        )
        logger.error(f"============== END OF LOG for {error_id}")
        
        # Determine status code and return generic message
        if isinstance(e, HTTPException):
            status_code = e.code
            # For HTTP errors, we might want to show some specific messages
            if status_code == 404:
                message = "Resource not found"
            elif status_code == 401:
                message = "Unauthorized access"
            elif status_code == 403:
                message = "Access forbidden"
            else:
                message = "An error occurred while processing your request"
        elif isinstance(e, SQLAlchemyError):
            db.session.rollback()
            status_code = 500
            message = "Database error occurred"
        else:
            status_code = 500
            message = "Internal server error"
        
        return {
            "message": message,
            "error_id": error_id,
            "status": status_code
        }, status_code

    def check_jsonify(self, data):
        make_response(data)
        return data
    

    @verify_bearer_token
    def get(self, public_app_id=None):
        """
        Read passport information.
        If public_app_id is provided, get specific user, otherwise list all users.
        """
        try:
            # single
            if public_app_id:
                public_app = (
                    db.session.query(PublicUserAuthenticationSettings)
                    .filter(PublicUserAuthenticationSettings.app_id == public_app_id)
                    .first()
                )
                if not public_app:
                    return self.check_jsonify(
                        {
                            "data": {
                                "enable_google_auth": None,
                                "enable_azure_ad_auth": None,
                                "enable_azure_api_auth": None,
                                "enable_email_whitelist": None,
                                "email_whitelist": [],
                            },
                            "success": True,
                            "message": "App ID Fetched successfully",
                            "status": 200,
                        }
                    ), 200

                return self.check_jsonify(
                    {
                        "data": {
                            "app_id": public_app.id,
                            "enable_google_auth": public_app.enable_google_auth,
                            "enable_azure_ad_auth": public_app.enable_azure_ad_auth,
                            "enable_azure_api_auth": public_app.enable_azure_api_auth,
                            "enable_email_whitelist": public_app.enable_email_whitelist,
                            "email_whitelist": public_app.email_whitelist_list,
                            "created_at": public_app.created_at.isoformat(),
                        },
                        "success": True,
                        "message": "App ID Fetched successfully",
                        "status": 200,
                    }
                ), 200

            else:
                return self.check_jsonify(
                    {
                        "data": {
                            "app_id": public_app_id,
                            "enable_google_auth": None,
                            "enable_azure_ad_auth": None,
                            "enable_azure_api_auth": None,
                            "enable_email_whitelist": None,
                            "email_whitelist": [],
                            "created_at": None,
                        },
                        "success": True,
                        "message": "Settings model not found, Temporary model Fetched successfully",
                        "status": 200,
                    }
                ), 200

        except Exception as e:
            return self.handle_error(e)


    @verify_bearer_token
    def post(self, public_app_id):
        """Create a new app id."""
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("enable_google_auth", type=bool, required=True, location="json")
            parser.add_argument("enable_azure_ad_auth", type=bool, required=True, location="json")
            parser.add_argument("enable_azure_api_auth", type=bool, required=True, location="json")
            parser.add_argument("enable_email_whitelist", type=bool, required=True, location="json")
            parser.add_argument("email_whitelist", type=list, required=True, location="json")

            args = parser.parse_args()
            enable_google_auth = args["enable_google_auth"]
            enable_azure_ad_auth = args["enable_azure_ad_auth"]
            enable_azure_api_auth = args["enable_azure_api_auth"]
            enable_email_whitelist = args["enable_email_whitelist"]
            email_whitelist = args["email_whitelist"]

            public_app = (
                db.session.query(PublicUserAuthenticationSettings)
                .filter(PublicUserAuthenticationSettings.app_id == public_app_id)
                .first()
            )
            if public_app:
                public_app.enable_google_auth = enable_google_auth
                public_app.enable_azure_ad_auth = enable_azure_ad_auth
                public_app.enable_azure_api_auth = enable_azure_api_auth
                public_app.enable_email_whitelist = enable_email_whitelist
                public_app.email_whitelist_list = email_whitelist

                db.session.commit()

                return self.check_jsonify(
                    {
                        "data": {
                            "app_id": public_app.app_id,
                            "enable_google_auth": public_app.enable_google_auth,
                            "enable_azure_ad_auth": public_app.enable_azure_ad_auth,
                            "enable_azure_api_auth": public_app.enable_azure_api_auth,
                            "enable_email_whitelist": public_app.enable_email_whitelist,
                            "email_whitelist": public_app.email_whitelist_list,
                            "created_at": public_app.created_at.isoformat(),
                        },
                        "success": True,
                        "message": "Settings Objects Updated Successfully",
                        "status": 200,
                    }
                ), 200

            else:
                # Create new end user
                public_app = PublicUserAuthenticationSettings(
                    app_id=public_app_id,
                    enable_google_auth=enable_google_auth,
                    enable_azure_ad_auth=enable_azure_ad_auth,
                    enable_azure_api_auth=enable_azure_api_auth,
                    enable_email_whitelist=enable_email_whitelist,
                    email_whitelist_list=email_whitelist,
                )

                db.session.add(public_app)
                db.session.commit()

                return self.check_jsonify(
                    {
                        "data": {
                            "app_id": public_app.app_id,
                            "enable_google_auth": public_app.enable_google_auth,
                            "enable_azure_ad_auth": public_app.enable_azure_ad_auth,
                            "enable_azure_api_auth": public_app.enable_azure_api_auth,
                            "enable_email_whitelist": public_app.enable_email_whitelist,
                            "email_whitelist": public_app.email_whitelist_list,
                            "created_at": public_app.created_at.isoformat(),
                        },
                        "success": True,
                        "message": "Setting objects Created Successfully",
                        "status": 201,
                    }
                ), 201

        except Exception as e:
            return self.handle_error(e)

    @verify_bearer_token
    def delete(self, public_app_id):
        """Delete a passport."""
        try:
            public_app = (
                db.session.query(PublicUserAuthenticationSettings)
                .filter(PublicUserAuthenticationSettings.app_id == public_app_id)
                .first()
            )
            if not public_app:
                raise NotFound("App ID not found")

            db.session.delete(public_app)
            db.session.commit()

            return self.check_jsonify(
                {
                    "data": {"public_app_id": public_app_id},
                    "success": True,
                    "message": "Setting Object deleted successfully",
                    "status": 200,
                }
            ), 200

        except Exception as e:
            return self.handle_error(e)


class GetAppId(Resource):
    def __init__(self):
        # Set up SGT offset (UTC+8)
        self.SGT = timezone(timedelta(hours=8))

    """Base resource for PublicAuthentication."""

    def handle_error(self, e):
        """Enhanced error handler with UUID logging"""
        # Generate unique error ID
        error_id = str(uuid.uuid4())
        
        # Prepare error details for logging
        error_details = {
            'error_type': e.__class__.__name__,
            'error_message': str(e),
            'traceback': traceback.format_exc(),
            'request_method': request.method,
            'request_url': request.url,
            'request_args': dict(request.args),
            'user_agent': request.headers.get('User-Agent', 'Unknown'),
            'remote_addr': request.remote_addr
        }
        
        # Log error with UUID using extra parameter
        logger.error(f"============== START OF LOG for {error_id}")
        logger.error(
            f"Error occurred: {error_details['error_type']} - {error_details['error_message']}\n"
            f"Request: {error_details['request_method']} {error_details['request_url']}\n"
            f"Args: {error_details['request_args']}\n"
            f"User-Agent: {error_details['user_agent']}\n"
            f"Remote Address: {error_details['remote_addr']}\n"
            f"Traceback:\n{error_details['traceback']}",
            extra={'error_id': error_id}
        )
        logger.error(f"============== END OF LOG for {error_id}")
        
        # Determine status code and return generic message
        if isinstance(e, HTTPException):
            status_code = e.code
            # For HTTP errors, we might want to show some specific messages
            if status_code == 404:
                message = "Resource not found"
            elif status_code == 401:
                message = "Unauthorized access"
            elif status_code == 403:
                message = "Access forbidden"
            else:
                message = "An error occurred while processing your request"
        elif isinstance(e, SQLAlchemyError):
            db.session.rollback()
            status_code = 500
            message = "Database error occurred"
        else:
            status_code = 500
            message = "Internal server error"
        
        return {
            "message": message,
            "error_id": error_id,
            "status": status_code
        }, status_code

    def check_jsonify(self, data):
        make_response(data)
        return data

    @verify_bearer_token
    def get(self, chat_id=None):
        """
        Read passport information.
        Get App ID
        """
        try:
            site = get_site_by_code(chat_id)
            if site:
                return self.check_jsonify(
                    {
                        "data": {
                            "app_id": str(site.app_id),
                            "chat_id": str(site.code),
                        },
                        "success": True,
                        "message": "App ID Fetched successfully",
                        "status": 200,
                    }
                ), 200
            else:
                raise NotFound("App ID not found")

        except Exception as e:
            return self.handle_error(e)


class GoogleAuth(Resource):
    def __init__(self):
        # Set up SGT offset (UTC+8)
        self.SGT = timezone(timedelta(hours=8))

    """Base resource for PublicAuthentication."""

    def handle_error(self, e):
        """Enhanced error handler with UUID logging"""
        # Generate unique error ID
        error_id = str(uuid.uuid4())
        
        # Prepare error details for logging
        error_details = {
            'error_type': e.__class__.__name__,
            'error_message': str(e),
            'traceback': traceback.format_exc(),
            'request_method': request.method,
            'request_url': request.url,
            'request_args': dict(request.args),
            'user_agent': request.headers.get('User-Agent', 'Unknown'),
            'remote_addr': request.remote_addr
        }
        
        # Log error with UUID using extra parameter
        logger.error(f"============== START OF LOG for {error_id}")
        logger.error(
            f"Error occurred: {error_details['error_type']} - {error_details['error_message']}\n"
            f"Request: {error_details['request_method']} {error_details['request_url']}\n"
            f"Args: {error_details['request_args']}\n"
            f"User-Agent: {error_details['user_agent']}\n"
            f"Remote Address: {error_details['remote_addr']}\n"
            f"Traceback:\n{error_details['traceback']}",
            extra={'error_id': error_id}
        )
        logger.error(f"============== END OF LOG for {error_id}")
        
        # Determine status code and return generic message
        if isinstance(e, HTTPException):
            status_code = e.code
            # For HTTP errors, we might want to show some specific messages
            if status_code == 404:
                message = "Resource not found"
            elif status_code == 401:
                message = "Unauthorized access"
            elif status_code == 403:
                message = "Access forbidden"
            else:
                message = "An error occurred while processing your request"
        elif isinstance(e, SQLAlchemyError):
            db.session.rollback()
            status_code = 500
            message = "Database error occurred"
        else:
            status_code = 500
            message = "Internal server error"
        
        return {
            "message": message,
            "error_id": error_id,
            "status": status_code
        }, status_code

    def check_jsonify(self, data):
        make_response(data)
        return data

    @verify_bearer_token
    def post(self):
        """
        Read passport information.
        Get JWT Google
        """
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("google_token", type=str, required=True, location="json")
            args = parser.parse_args()
            google_token = args["google_token"]

            headers = {"Authorization": f"Bearer {google_token}"}
            google_response = requests.get("https://www.googleapis.com/oauth2/v3/userinfo", headers=headers)

            google_user_info = google_response.json()
            if "error" in google_user_info:
                return self.handle_error(google_user_info)

            user_id = google_user_info.get("sub")
            email = google_user_info.get("email")
            name = google_user_info.get("name")

            payload = {
                "sub": user_id,
                "email": email,
                "name": name,
                "iat": datetime.utcnow(),
                "exp": datetime.utcnow() + timedelta(seconds=86400),
                "auth_type": "GOOGLE",
            }

            NIE_API_TOKEN = os.environ.get("NIE_API_TOKEN")

            token = jwt.encode(payload, NIE_API_TOKEN, algorithm="HS256")

            # Create response
            response = make_response(
                jsonify(
                    {
                        "data": {"token": token, "metadata": google_response.json()},
                        "success": True,
                        "message": "App ID Fetched successfully",
                        "status": 200,
                    }
                )
            )

            # Set the token in an HTTP-only cookie
            response.set_cookie("auth_nie_dify", token, httponly=True, secure=True, samesite="None", max_age=86400)

            response.set_cookie(
                "auth_nie_dify_email", email, httponly=True, secure=True, samesite="None", max_age=86400
            )

            return response

        except Exception as e:
            return self.handle_error(e)


class AzureAuth(Resource):
    def __init__(self):
        # Set up SGT offset (UTC+8)
        self.SGT = timezone(timedelta(hours=8))

    """Base resource for PublicAuthentication."""

    def handle_error(self, e):
        """Enhanced error handler with UUID logging"""
        # Generate unique error ID
        error_id = str(uuid.uuid4())
        
        # Prepare error details for logging
        error_details = {
            'error_type': e.__class__.__name__,
            'error_message': str(e),
            'traceback': traceback.format_exc(),
            'request_method': request.method,
            'request_url': request.url,
            'request_args': dict(request.args),
            'user_agent': request.headers.get('User-Agent', 'Unknown'),
            'remote_addr': request.remote_addr
        }
        
        # Log error with UUID using extra parameter
        logger.error(f"============== START OF LOG for {error_id}")
        logger.error(
            f"Error occurred: {error_details['error_type']} - {error_details['error_message']}\n"
            f"Request: {error_details['request_method']} {error_details['request_url']}\n"
            f"Args: {error_details['request_args']}\n"
            f"User-Agent: {error_details['user_agent']}\n"
            f"Remote Address: {error_details['remote_addr']}\n"
            f"Traceback:\n{error_details['traceback']}",
            extra={'error_id': error_id}
        )
        logger.error(f"============== END OF LOG for {error_id}")
        
        # Determine status code and return generic message
        if isinstance(e, HTTPException):
            status_code = e.code
            # For HTTP errors, we might want to show some specific messages
            if status_code == 404:
                message = "Resource not found"
            elif status_code == 401:
                message = "Unauthorized access"
            elif status_code == 403:
                message = "Access forbidden"
            else:
                message = "An error occurred while processing your request"
        elif isinstance(e, SQLAlchemyError):
            db.session.rollback()
            status_code = 500
            message = "Database error occurred"
        else:
            status_code = 500
            message = "Internal server error"
        
        return {
            "message": message,
            "error_id": error_id,
            "status": status_code
        }, status_code

    def check_jsonify(self, data):
        make_response(data)
        return data

    @verify_bearer_token
    def post(self):
        """
        Read passport information.
        Get JWT Google
        """
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("azure_token", type=str, required=True, location="json")
            args = parser.parse_args()
            azure_token = args["azure_token"]
            print("azure_token: ", azure_token)

            azure_response = requests.get(
                "https://graph.microsoft.com/v1.0/me",
                headers={"Authorization": f"Bearer {azure_token}"},
                params={"$select": "id,displayName,givenName,mail"},
                timeout=30,
            )

            azure_user_info = azure_response.json()
            if "error" in azure_user_info:
                return self.handle_error(azure_user_info)

            email = azure_user_info["mail"]

            if "displayName" not in azure_user_info:
                displayName = email.split("@")[0]
            else:
                displayName = azure_user_info["displayName"]

            id = azure_user_info["id"]

            payload = {
                "user_id": id,
                "email": email,
                "name": displayName,
                "iat": datetime.utcnow(),
                "exp": datetime.utcnow() + timedelta(seconds=86400),
                "auth_type": "AZURE",
            }

            NIE_API_TOKEN = os.environ.get("NIE_API_TOKEN")

            token = jwt.encode(payload, NIE_API_TOKEN, algorithm="HS256")

            # Create response
            response = make_response(
                jsonify(
                    {
                        "data": {"token": token, "metadata": azure_user_info},
                        "success": True,
                        "message": "App ID Fetched successfully",
                        "status": 200,
                    }
                )
            )

            # Set the token in an HTTP-only cookie
            response.set_cookie("auth_nie_dify", token, httponly=True, secure=True, samesite="None", max_age=86400)

            response.set_cookie(
                "auth_nie_dify_email", email, httponly=True, secure=True, samesite="None", max_age=86400
            )

            return response

        except Exception as e:
            return self.handle_error(e)


class NieApiVerify(Resource):
    def __init__(self):
        # Set up SGT offset (UTC+8)
        self.SGT = timezone(timedelta(hours=8))

    """Base resource for PublicAuthentication."""

    def handle_error(self, e):
        """Enhanced error handler with UUID logging"""
        # Generate unique error ID
        error_id = str(uuid.uuid4())
        
        # Prepare error details for logging
        error_details = {
            'error_type': e.__class__.__name__,
            'error_message': str(e),
            'traceback': traceback.format_exc(),
            'request_method': request.method,
            'request_url': request.url,
            'request_args': dict(request.args),
            'user_agent': request.headers.get('User-Agent', 'Unknown'),
            'remote_addr': request.remote_addr
        }
        
        # Log error with UUID using extra parameter
        logger.error(f"============== START OF LOG for {error_id}")
        logger.error(
            f"Error occurred: {error_details['error_type']} - {error_details['error_message']}\n"
            f"Request: {error_details['request_method']} {error_details['request_url']}\n"
            f"Args: {error_details['request_args']}\n"
            f"User-Agent: {error_details['user_agent']}\n"
            f"Remote Address: {error_details['remote_addr']}\n"
            f"Traceback:\n{error_details['traceback']}",
            extra={'error_id': error_id}
        )
        logger.error(f"============== END OF LOG for {error_id}")
        
        # Determine status code and return generic message
        if isinstance(e, HTTPException):
            status_code = e.code
            # For HTTP errors, we might want to show some specific messages
            if status_code == 404:
                message = "Resource not found"
            elif status_code == 401:
                message = "Unauthorized access"
            elif status_code == 403:
                message = "Access forbidden"
            else:
                message = "An error occurred while processing your request"
        elif isinstance(e, SQLAlchemyError):
            db.session.rollback()
            status_code = 500
            message = "Database error occurred"
        else:
            status_code = 500
            message = "Internal server error"
        
        return {
            "message": message,
            "error_id": error_id,
            "status": status_code
        }, status_code

    def check_jsonify(self, data):
        make_response(data)
        return data


    @verify_bearer_token
    def post(self):
        """
        Read passport information.
        Get JWT Google
        """
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("session_token", type=str, required=True, location="json")
            parser.add_argument("userid", type=str, required=True, location="json")
            parser.add_argument("email", type=str, required=True, location="json")
            parser.add_argument("name", type=str, required=False, location="json")
            args = parser.parse_args()
            session_token = args["session_token"]
            user_id = args["userid"]
            email = args["email"]
            name = args["name"]

            NIE_SUB_KEY = os.environ.get("NIE_SUB_KEY")
            NIE_API_URL = os.environ.get("NIE_API_URL")

            headers = {
                "Ocp-Apim-Subscription-Key": NIE_SUB_KEY,
                "sessionToken": f"{session_token}",
                "UserId": f"{user_id}",
            }
            nie_api_response = requests.post(f"{NIE_API_URL}/verify", headers=headers)

            nie_verify_info = nie_api_response.json()

            if not nie_verify_info["success"]:
                return self.handle_error(nie_verify_info)

            userId = nie_verify_info.get("userId")

            payload = {
                "userId": userId,
                "name": name,
                "email": email,
                "iat": datetime.utcnow(),
                "exp": datetime.utcnow() + timedelta(seconds=86400),
                "auth_type": "AZURE_API",
            }

            NIE_API_TOKEN = os.environ.get("NIE_API_TOKEN")

            token = jwt.encode(payload, NIE_API_TOKEN, algorithm="HS256")

            # Create response
            response = make_response(
                jsonify(
                    {
                        "data": {
                            "userId": userId,
                            "email": email,
                            "token": token,
                        },
                        "success": True,
                        "message": "NIE Token API Verified successfully",
                        "status": 200,
                    }
                )
            )

            # Set the token in an HTTP-only cookie
            response.set_cookie("auth_nie_dify", token, httponly=True, secure=True, samesite="None", max_age=86400)

            response.set_cookie(
                "auth_nie_dify_email", email, httponly=True, secure=True, samesite="None", max_age=86400
            )

            return response

        except Exception as e:
            return self.handle_error(e)


class NieApiSessionVerify(Resource):
    def __init__(self):
        # Set up SGT offset (UTC+8)
        self.SGT = timezone(timedelta(hours=8))

    """Base resource for PublicAuthentication."""

    def handle_error(self, e):
        """Enhanced error handler with UUID logging"""
        # Generate unique error ID
        error_id = str(uuid.uuid4())
        
        # Prepare error details for logging
        error_details = {
            'error_type': e.__class__.__name__,
            'error_message': str(e),
            'traceback': traceback.format_exc(),
            'request_method': request.method,
            'request_url': request.url,
            'request_args': dict(request.args),
            'user_agent': request.headers.get('User-Agent', 'Unknown'),
            'remote_addr': request.remote_addr
        }
        
        # Log error with UUID using extra parameter
        logger.error(f"============== START OF LOG for {error_id}")
        logger.error(
            f"Error occurred: {error_details['error_type']} - {error_details['error_message']}\n"
            f"Request: {error_details['request_method']} {error_details['request_url']}\n"
            f"Args: {error_details['request_args']}\n"
            f"User-Agent: {error_details['user_agent']}\n"
            f"Remote Address: {error_details['remote_addr']}\n"
            f"Traceback:\n{error_details['traceback']}",
            extra={'error_id': error_id}
        )
        logger.error(f"============== END OF LOG for {error_id}")
        
        # Determine status code and return generic message
        if isinstance(e, HTTPException):
            status_code = e.code
            # For HTTP errors, we might want to show some specific messages
            if status_code == 404:
                message = "Resource not found"
            elif status_code == 401:
                message = "Unauthorized access"
            elif status_code == 403:
                message = "Access forbidden"
            else:
                message = "An error occurred while processing your request"
        elif isinstance(e, SQLAlchemyError):
            db.session.rollback()
            status_code = 500
            message = "Database error occurred"
        else:
            status_code = 500
            message = "Internal server error"
        
        return {
            "message": message,
            "error_id": error_id,
            "status": status_code
        }, status_code

    def check_jsonify(self, data):
        make_response(data)
        return data


    @verify_bearer_token
    def post(self):
        """
        Read passport information.
        Get JWT Google
        """
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("session_token", type=str, required=True, location="json")
            args = parser.parse_args()
            session_token = args["session_token"]

            NIE_SUB_KEY = os.environ.get("NIE_SUB_KEY")
            NIE_API_URL = os.environ.get("NIE_API_URL")

            headers = {
                "Ocp-Apim-Subscription-Key": NIE_SUB_KEY,
                "sessionToken": f"{session_token}",
            }
            nie_api_response = requests.post(f"{NIE_API_URL}/SessionUserInfo", headers=headers)
            nie_verify_info = nie_api_response.json()

            if 'fault' in nie_verify_info:
                return self.handle_error(nie_verify_info)

            userId = nie_verify_info.get("result").get("attributes").get("userId")
            email = nie_verify_info.get("result").get("attributes").get("email")
            existing_account = get_account_by_email(email)
            if not existing_account:
                return self.check_jsonify(
                    {
                        "data": {
                            "userId": userId,
                            "email": email,
                        },
                        "success": False,
                        "message": "Account is not found",
                        "status": 404,
                    }
                ), 404

            tenants = get_join_tenants_by_account_id(existing_account.id)
            if not tenants:
                return self.check_jsonify(
                    {
                        "data": {
                            "userId": userId,
                            "email": email,
                        },
                        "success": False,
                        "message": "Account has been registered but does not belong to any tenant.\
                              Please, contact admin to register into tenant",
                        "status": 404,
                    }
                ), 404

            payload = {
                "userId": userId,
                "email": email,
                "iat": datetime.utcnow(),
                "exp": datetime.utcnow() + timedelta(seconds=86400),
            }

            NIE_API_TOKEN = os.environ.get("NIE_API_TOKEN")

            token = jwt.encode(payload, NIE_API_TOKEN, algorithm="HS256")

            # Create response
            response = make_response(
                jsonify(
                    {
                        "data": {
                            "userId": userId,
                            "email": email,
                            "token": token,
                        },
                        "success": True,
                        "message": "NIE Session Token API Verified successfully",
                        "status": 200,
                    }
                )
            )

            return response

        except Exception as e:
            return self.handle_error(e)


class CheckCookies(Resource):
    def __init__(self):
        # Set up SGT offset (UTC+8)
        self.SGT = timezone(timedelta(hours=8))

    """Base resource for PublicAuthentication."""

    def handle_error(self, e):
        """Enhanced error handler with UUID logging"""
        # Generate unique error ID
        error_id = str(uuid.uuid4())
        
        # Prepare error details for logging
        error_details = {
            'error_type': e.__class__.__name__,
            'error_message': str(e),
            'traceback': traceback.format_exc(),
            'request_method': request.method,
            'request_url': request.url,
            'request_args': dict(request.args),
            'user_agent': request.headers.get('User-Agent', 'Unknown'),
            'remote_addr': request.remote_addr
        }
        
        # Log error with UUID using extra parameter
        logger.error(f"============== START OF LOG for {error_id}")
        logger.error(
            f"Error occurred: {error_details['error_type']} - {error_details['error_message']}\n"
            f"Request: {error_details['request_method']} {error_details['request_url']}\n"
            f"Args: {error_details['request_args']}\n"
            f"User-Agent: {error_details['user_agent']}\n"
            f"Remote Address: {error_details['remote_addr']}\n"
            f"Traceback:\n{error_details['traceback']}",
            extra={'error_id': error_id}
        )
        logger.error(f"============== END OF LOG for {error_id}")
        
        # Determine status code and return generic message
        if isinstance(e, HTTPException):
            status_code = e.code
            # For HTTP errors, we might want to show some specific messages
            if status_code == 404:
                message = "Resource not found"
            elif status_code == 401:
                message = "Unauthorized access"
            elif status_code == 403:
                message = "Access forbidden"
            else:
                message = "An error occurred while processing your request"
        elif isinstance(e, SQLAlchemyError):
            db.session.rollback()
            status_code = 500
            message = "Database error occurred"
        else:
            status_code = 500
            message = "Internal server error"
        
        return {
            "message": message,
            "error_id": error_id,
            "status": status_code
        }, status_code

    def check_jsonify(self, data):
        make_response(data)
        return data

    @verify_bearer_token
    def get(self):
        """
        Read passpcort information.
        Get Check  Cookies Google
        """
        try:
            # Get all cookies
            result_dict = {}
            for key, value in request.cookies.items():
                result_dict[key] = value

            if result_dict:
                return self.check_jsonify(
                    {"data": result_dict, "success": True, "message": "Cookies Fetched", "status": 200}
                ), 200
            else:
                return self.check_jsonify(
                    {"data": {}, "success": False, "message": "Cookies is Empety", "status": 404}
                ), 404

        except Exception as e:
            return self.handle_error(e)


class DeleteCookies(Resource):
    def __init__(self):
        # Set up SGT offset (UTC+8)
        self.SGT = timezone(timedelta(hours=8))

    """Base resource for PublicAuthentication."""

    def handle_error(self, e):
        """Enhanced error handler with UUID logging"""
        # Generate unique error ID
        error_id = str(uuid.uuid4())
        
        # Prepare error details for logging
        error_details = {
            'error_type': e.__class__.__name__,
            'error_message': str(e),
            'traceback': traceback.format_exc(),
            'request_method': request.method,
            'request_url': request.url,
            'request_args': dict(request.args),
            'user_agent': request.headers.get('User-Agent', 'Unknown'),
            'remote_addr': request.remote_addr
        }
        
        # Log error with UUID using extra parameter
        logger.error(f"============== START OF LOG for {error_id}")
        logger.error(
            f"Error occurred: {error_details['error_type']} - {error_details['error_message']}\n"
            f"Request: {error_details['request_method']} {error_details['request_url']}\n"
            f"Args: {error_details['request_args']}\n"
            f"User-Agent: {error_details['user_agent']}\n"
            f"Remote Address: {error_details['remote_addr']}\n"
            f"Traceback:\n{error_details['traceback']}",
            extra={'error_id': error_id}
        )
        logger.error(f"============== END OF LOG for {error_id}")
        
        # Determine status code and return generic message
        if isinstance(e, HTTPException):
            status_code = e.code
            # For HTTP errors, we might want to show some specific messages
            if status_code == 404:
                message = "Resource not found"
            elif status_code == 401:
                message = "Unauthorized access"
            elif status_code == 403:
                message = "Access forbidden"
            else:
                message = "An error occurred while processing your request"
        elif isinstance(e, SQLAlchemyError):
            db.session.rollback()
            status_code = 500
            message = "Database error occurred"
        else:
            status_code = 500
            message = "Internal server error"
        
        return {
            "message": message,
            "error_id": error_id,
            "status": status_code
        }, status_code
    def check_jsonify(self, data):
        make_response(data)
        return data


    @verify_bearer_token
    def post(self):
        """
        Read passport information.
        Get JWT Google
        """
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("cookie", type=str, required=True, location="json")
            args = parser.parse_args()
            cookie = args["cookie"]

            # Create response
            response = make_response(
                jsonify(
                    {
                        "data": {"cookie": cookie},
                        "success": True,
                        "message": f"cookie {cookie} deleted succesfully",
                        "status": 200,
                    }
                )
            )

            # Set the token in an HTTP-only cookie
            response.set_cookie(cookie, "", httponly=True, secure=True, samesite="None", max_age=86400)

            return response

        except Exception as e:
            return self.handle_error(e)


class AdminSSOAuth(Resource):
    def __init__(self):
        # Set up SGT offset (UTC+8)
        self.SGT = timezone(timedelta(hours=8))

    """Base resource for PublicAuthentication."""

    def handle_error(self, e):
        """Enhanced error handler with UUID logging"""
        # Generate unique error ID
        error_id = str(uuid.uuid4())
        
        # Prepare error details for logging
        error_details = {
            'error_type': e.__class__.__name__,
            'error_message': str(e),
            'traceback': traceback.format_exc(),
            'request_method': request.method,
            'request_url': request.url,
            'request_args': dict(request.args),
            'user_agent': request.headers.get('User-Agent', 'Unknown'),
            'remote_addr': request.remote_addr
        }
        
        # Log error with UUID using extra parameter
        logger.error(f"============== START OF LOG for {error_id}")
        logger.error(
            f"Error occurred: {error_details['error_type']} - {error_details['error_message']}\n"
            f"Request: {error_details['request_method']} {error_details['request_url']}\n"
            f"Args: {error_details['request_args']}\n"
            f"User-Agent: {error_details['user_agent']}\n"
            f"Remote Address: {error_details['remote_addr']}\n"
            f"Traceback:\n{error_details['traceback']}",
            extra={'error_id': error_id}
        )
        logger.error(f"============== END OF LOG for {error_id}")

        
        # Determine status code and return generic message
        if isinstance(e, HTTPException):
            status_code = e.code
            # For HTTP errors, we might want to show some specific messages
            if status_code == 404:
                message = "Resource not found"
            elif status_code == 401:
                message = "Unauthorized access"
            elif status_code == 403:
                message = "Access forbidden"
            else:
                message = "An error occurred while processing your request"
        elif isinstance(e, SQLAlchemyError):
            db.session.rollback()
            status_code = 500
            message = "Database error occurred"
        else:
            status_code = 500
            message = "Internal server error"
        
        return {
            "message": message,
            "error_id": error_id,
            "status": status_code
        }, status_code

    def check_jsonify(self, data):
        make_response(data)
        return data

    @verify_bearer_token
    def post(self):
        """
        POST information.
        """
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("email", type=str, required=True, location="json")
            args = parser.parse_args()

            email = args["email"]

            existing_account = get_account_by_email(email)
            if not existing_account:
                return self.check_jsonify(
                    {
                        "data": {"access_token": "", "refresh_token": ""},
                        "success": False,
                        "message": "Account is not found",
                        "status": 404,
                    }
                ), 404

            # Get the tenant the user belongs to
            tenant = get_join_tenants_by_account_id(existing_account.id)
            if not tenant:
                return self.check_jsonify(
                    {
                        "data": {"access_token": "", "refresh_token": ""},
                        "success": False,
                        "message": "Account has been registered but does not belong to any tenant.\
                              Please, contact admin to register into tenant",
                        "status": 404,
                    }
                ), 404
  
            # Get members of the first tenant (you might want to adjust this logic)
            tenant_members = get_tenant_members_by_tenant_id(tenant.id)
            member_found = next((member for member in tenant_members if member.email == email), None)
            if not member_found:
                return self.check_jsonify(
                    {
                        "data": {"access_token": "", "refresh_token": ""},
                        "success": False,
                        "message": "Account is not a tenant member",
                        "status": 404,
                    }
                ), 404

            if existing_account and existing_account.status != "active":
                return self.check_jsonify(
                    {
                        "data": {"access_token": "", "refresh_token": ""},
                        "success": False,
                        "message": "Account is not Active",
                        "status": 404,
                    }
                ), 404

            # Use the new AccountService.login method
            token_pair = AccountService.login(account=existing_account, ip_address=extract_remote_ip(request))
            # AccountService.reset_login_error_rate_limit(email)
            
            return self.check_jsonify(
                {
                    "data": {
                        "access_token": token_pair.access_token,
                        "refresh_token": token_pair.refresh_token
                    },
                    "success": True,
                    "message": "Login successful",
                    "status": 200,
                }
            ), 200

        except Exception as e:
            print("====== e: ", e)
            return self.handle_error(e)


    @verify_bearer_token
    def delete(self, account_id):
        """Delete an account"""
        try:
            # Find the account in the Dify database
            sql_query = """
                SELECT * FROM accounts
                WHERE id = :account_id
            """
            account = execute_dify_query(sql_query, {"account_id": account_id})
            
            if not account:
                raise NotFound("Account not found")

            # Delete account using raw SQL query
            sql_query = """
                DELETE FROM accounts
                WHERE id = :account_id
            """
            execute_dify_query(sql_query, {"account_id": account_id})

            return self.check_jsonify(
                {"success": True, "message": "Account deletion initiated successfully", "status": 200}
            ), 200
        except Exception as e:
            return self.handle_error(e)


class PublicAuthenticationAdminLog(Resource):
    def __init__(self):
        # Set up SGT offset (UTC+8)
        self.SGT = timezone(timedelta(hours=8))

    """Base resource for PublicAuthentication."""

    def handle_error(self, e):
        """Enhanced error handler with UUID logging"""
        # Generate unique error ID
        error_id = str(uuid.uuid4())
        
        # Prepare error details for logging
        error_details = {
            'error_type': e.__class__.__name__,
            'error_message': str(e),
            'traceback': traceback.format_exc(),
            'request_method': request.method,
            'request_url': request.url,
            'request_args': dict(request.args),
            'user_agent': request.headers.get('User-Agent', 'Unknown'),
            'remote_addr': request.remote_addr
        }
        
        # Log error with UUID using extra parameter
        logger.error(f"============== START OF LOG for {error_id}")
        logger.error(
            f"Error occurred: {error_details['error_type']} - {error_details['error_message']}\n"
            f"Request: {error_details['request_method']} {error_details['request_url']}\n"
            f"Args: {error_details['request_args']}\n"
            f"User-Agent: {error_details['user_agent']}\n"
            f"Remote Address: {error_details['remote_addr']}\n"
            f"Traceback:\n{error_details['traceback']}",
            extra={'error_id': error_id}
        )
        logger.error(f"============== END OF LOG for {error_id}")
        
        # Determine status code and return generic message
        if isinstance(e, HTTPException):
            status_code = e.code
            # For HTTP errors, we might want to show some specific messages
            if status_code == 404:
                message = "Resource not found"
            elif status_code == 401:
                message = "Unauthorized access"
            elif status_code == 403:
                message = "Access forbidden"
            else:
                message = "An error occurred while processing your request"
        elif isinstance(e, SQLAlchemyError):
            db.session.rollback()
            status_code = 500
            message = "Database error occurred"
        else:
            status_code = 500
            message = "Internal server error"
        
        return {
            "message": message,
            "error_id": error_id,
            "status": status_code
        }, status_code

    def check_jsonify(self, data):
        make_response(data)
        return data

  
    @verify_bearer_token
    def get(self):
        """
        Read passport information.
        If user_id is provided, get specific user, otherwise list all users.
        """
        try:
            # List all users (with pagination)
            # Get page from request and add 1 since SQLAlchemy pagination is 1-based
            page = request.args.get("page", 0, type=int) + 1  # Default to 0, add 1 for SQLAlchemy
            per_page = request.args.get("per_page", 999, type=int)
            search = request.args.get("search", "")

            # Get time filter as integer (days)
            days_filter = request.args.get("days_filter", 0, type=int)  # Default to 0 (all time)

            # Get sort parameter, default to "-timestamp" for descending
            sort = request.args.get("sort", "-timestamp")

            # Build query with filters
            query = db.session.query(PublicAdminAuthenticationLog)

            if search:
                query = query.filter(
                    db.or_(
                        PublicAdminAuthenticationLog.username.ilike(f"%{search}%"),
                        PublicAdminAuthenticationLog.email.ilike(f"%{search}%"),
                    )
                )

            # Apply time period filter
            # Apply time period filter only if days_filter is provided and not 0
            if days_filter is not None and days_filter > 0:
                now = datetime.now(self.SGT)
                start_date = now - timedelta(days=days_filter)
                # Convert to UTC for database query
                start_date_utc = start_date.astimezone(UTC)
                query = query.filter(PublicAdminAuthenticationLog.timestamp >= start_date_utc)
            elif days_filter == 0:
                # For days_filter = 0, filter records from today only
                today_start = datetime.now(self.SGT).replace(hour=0, minute=0, second=0, microsecond=0)
                today_start_utc = today_start.astimezone(UTC)
                query = query.filter(PublicAdminAuthenticationLog.timestamp >= today_start_utc)

            # Apply sorting based on "-timestamp" notation
            if sort.startswith("-"):
                query = query.order_by(PublicAdminAuthenticationLog.timestamp.desc())
            else:
                query = query.order_by(PublicAdminAuthenticationLog.timestamp.asc())

            users = query.paginate(page=page, per_page=per_page, error_out=False)

            return self.check_jsonify(
                {
                    "data": {
                        "result": [
                            {
                                "id": user.id,
                                "username": user.username,
                                "email": user.email,
                                "auth_type": user.auth_type.value,
                                "timestamp": user.timestamp.replace(tzinfo=UTC)
                                .astimezone(self.SGT)
                                .strftime("%d-%m-%Y %H:%M"),
                            }
                            for user in users.items
                        ],
                        "pagination": {
                            "total_count": users.total,
                            "total_pages": users.pages,
                            "current_page": users.page - 1,  # Subtract 1 to return to zero-based indexing
                            "limit": per_page,
                            "next_page": (users.next_num - 1) if users.has_next else None,  # Subtract 1 for zero-based
                            "prev_page": (users.prev_num - 1) if users.has_prev else None,  # Subtract 1 for zero-based
                        },
                    },
                    "success": True,
                    "message": "User list Fetched Successfully",
                    "status": 200,
                }
            ), 200

        except Exception as e:
            return self.handle_error(e)

 
    @verify_bearer_token
    def post(self):
        """Create a new passport."""
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("email", type=str, required=True, location="json")
            parser.add_argument("auth_type", type=str, required=True, location="json")
            args = parser.parse_args()

            email = args["email"]
            auth_type = args["auth_type"]

            existing_account = get_account_by_email(email)
            if not existing_account:
                raise NotFound("Account is not found")

            # Create new end user
            public_user = PublicAdminAuthenticationLog(username=email.split("@")[0], email=email, auth_type=auth_type)

            db.session.add(public_user)
            db.session.commit()

            return self.check_jsonify(
                {
                    "data": {
                        "id": public_user.id,
                        "username": public_user.username,
                        "email": public_user.email,
                        "auth_type": public_user.auth_type.value,
                        "timestamp": public_user.timestamp.replace(tzinfo=UTC)
                        .astimezone(self.SGT)
                        .strftime("%d-%m-%Y %H:%M"),
                    },
                    "success": True,
                    "message": "Admin Log Created Successfully",
                    "status": 201,
                }
            ), 201

        except Exception as e:
            return self.handle_error(e)

    @verify_bearer_token
    def delete(self, public_admin_log_id):
        """Delete a passport."""
        try:
            public_user = (
                db.session.query(PublicAdminAuthenticationLog)
                .filter(PublicAdminAuthenticationLog.id == public_admin_log_id)
                .first()
            )
            if not public_user:
                raise NotFound("Log Admin ID not found")

            db.session.delete(public_user)
            db.session.commit()

            return self.check_jsonify(
                {
                    "data": {"public_admin_log_id": public_admin_log_id},
                    "success": True,
                    "message": "User deleted successfully",
                    "status": 200,
                }
            ), 200

        except Exception as e:
            return self.handle_error(e)


# Preventing CORS
class ForwardApi(Resource):
    def __init__(self):
        # Set up SGT offset (UTC+8)
        self.SGT = timezone(timedelta(hours=8))

    """Base resource for PublicAuthentication."""

    def handle_error(self, e):
        """Enhanced error handler with UUID logging"""
        # Generate unique error ID
        error_id = str(uuid.uuid4())
        
        # Prepare error details for logging
        error_details = {
            'error_type': e.__class__.__name__,
            'error_message': str(e),
            'traceback': traceback.format_exc(),
            'request_method': request.method,
            'request_url': request.url,
            'request_args': dict(request.args),
            'user_agent': request.headers.get('User-Agent', 'Unknown'),
            'remote_addr': request.remote_addr
        }

        # Log error with UUID using extra parameter
        logger.error(
            f"A. Error occurred: {error_details['error_type']} - {error_details['error_message']}\n"
            f"B. Request: {error_details['request_method']} {error_details['request_url']}\n"
            f"C. Args: {error_details['request_args']}\n"
            f"D. User-Agent: {error_details['user_agent']}\n"
            f"E. Remote Address: {error_details['remote_addr']}\n"
            f"F. Traceback:\n{error_details['traceback']}",
            extra={'error_id': error_id}
        )
        logger.error(f"============== END OF LOG for {error_id}",extra={'error_id': error_id})
        
        # Determine status code and return generic message
        if isinstance(e, HTTPException):
            status_code = e.code
            # For HTTP errors, we might want to show some specific messages
            if status_code == 404:
                message = "Resource not found"
            elif status_code == 401:
                message = "Unauthorized access"
            elif status_code == 403:
                message = "Access forbidden"
            else:
                message = "An error occurred while processing your request"
        elif isinstance(e, SQLAlchemyError):
            db.session.rollback()
            status_code = 500
            message = "Database error occurred"
        else:
            status_code = 500
            message = "Internal server error"
        
        return {
            "message": message,
            "error_id": error_id,
            "status": status_code
        }, status_code


    @verify_bearer_token
    def post(self):
        """
        Forward API requests to the specified endpoint
        """
        try:
            parser = reqparse.RequestParser()
            parser.add_argument("endpoint", type=str, required=True, location="json")
            parser.add_argument("payload", type=dict, required=False, location="json")
            parser.add_argument("headers", type=dict, required=False, location="json")
            args = parser.parse_args()

            # Get headers from payload if provided, otherwise use default headers
            custom_headers = args.get("headers", {})

            response = requests.request(
                method=request.method,
                url=args["endpoint"],
                headers=custom_headers,
                json=args.get("payload"),
            )

            if response.status_code <200 or response.status_code>=300:
                raise Exception(f"Request failed with status code {str(response.status_code)}: ")

            return response.json() if response.headers.get("content-type", "").startswith(
                "application/json"
            ) else response.text, response.status_code

        except Exception as e:
            return self.handle_error(e)


class EndUsersMetadata(Resource):
    def __init__(self):
        # Set up SGT offset (UTC+8)
        self.SGT = timezone(timedelta(hours=8))

    """Base resource for PublicAuthentication."""

    def handle_error(self, e):
        """Enhanced error handler with UUID logging"""
        # Generate unique error ID
        error_id = str(uuid.uuid4())
        
        # Prepare error details for logging
        error_details = {
            'error_type': e.__class__.__name__,
            'error_message': str(e),
            'traceback': traceback.format_exc(),
            'request_method': request.method,
            'request_url': request.url,
            'request_args': dict(request.args),
            'user_agent': request.headers.get('User-Agent', 'Unknown'),
            'remote_addr': request.remote_addr
        }
        
        # Log error with UUID using extra parameter
        logger.error(f"============== START OF LOG for {error_id}")
        logger.error(
            f"Error occurred: {error_details['error_type']} - {error_details['error_message']}\n"
            f"Request: {error_details['request_method']} {error_details['request_url']}\n"
            f"Args: {error_details['request_args']}\n"
            f"User-Agent: {error_details['user_agent']}\n"
            f"Remote Address: {error_details['remote_addr']}\n"
            f"Traceback:\n{error_details['traceback']}",
            extra={'error_id': error_id}
        )
        logger.error(f"============== END OF LOG for {error_id}")
        
        # Determine status code and return generic message
        if isinstance(e, HTTPException):
            status_code = e.code
            # For HTTP errors, we might want to show some specific messages
            if status_code == 404:
                message = "Resource not found"
            elif status_code == 401:
                message = "Unauthorized access"
            elif status_code == 403:
                message = "Access forbidden"
            else:
                message = "An error occurred while processing your request"
        elif isinstance(e, SQLAlchemyError):
            db.session.rollback()
            status_code = 500
            message = "Database error occurred"
        else:
            status_code = 500
            message = "Internal server error"
        
        return {
            "message": message,
            "error_id": error_id,
            "status": status_code
        }, status_code

    def check_jsonify(self, data):
        make_response(data)
        return data

  
    @verify_bearer_token
    def get(self,enduser_id=None):
        """
        Read name on enduser table information.
        """
        try:

            if not enduser_id:
                raise NotFound("End User ID not found")

            result={}
            result = get_name_by_enduser_session_id(enduser_id)
            result['id'] = str(result['id'])
            result['tenant_id'] = str(result['tenant_id'])
            result['app_id'] = str(result['app_id'])
            result['session_id'] = str(result['session_id'])
            result['created_at'] = result['created_at'].astimezone(self.SGT).isoformat()
            result['updated_at'] = result['updated_at'].astimezone(self.SGT).isoformat()
            return self.check_jsonify(
                {
                    "data": {
                        "result": result
                    },
                    "success": True,
                    "message": "End User Fetched Successfully",
                    "status": 200,
                    }
            ), 200

        except Exception as e:
            return self.handle_error(e)

 
    @verify_bearer_token
    def post(self):
        """Create a new passport."""
        try:
            
            conversation_id=None
            workflow_run_id=None
            message_id=None
            result_dict = {}
            for key, value in request.cookies.items():
                result_dict[key] = value

            parser = reqparse.RequestParser()
            
            parser.add_argument("chat_type", type=str, required=True, location="json")
            parser.add_argument("conversation_id", type=str, required=False, location="json")
            parser.add_argument("workflow_run_id", type=str, required=False, location="json")
            parser.add_argument("message_id", type=str, required=False, location="json")
            args = parser.parse_args()

            chat_type = args["chat_type"]
            if chat_type == "workflow":
                workflow_run_id = args["workflow_run_id"]
                from_end_user_id=None
                workflow_app_log = get_createdby_from_workflow_run_id(workflow_run_id)
                if workflow_app_log:
                    if workflow_app_log.created_by:
                        from_end_user_id = str(workflow_app_log.created_by)
                    else:
                        raise NotFound("created_by on workflow_app_log Model not found")
                else:
                    raise NotFound("workflow_app_log model is not found")

            elif chat_type == "text-generator":
                message_id = args["message_id"]
                from_end_user_id=None
                messages = get_conversation_id_from_message_id(message_id)
                if messages:
                    if messages.conversation_id:
                        conversation_id = str(messages.conversation_id)
                    else:
                        raise NotFound("conversation_id on workflow_app_log Model not found")
                else:
                    raise NotFound("messages model is not found")
                
                if conversation_id:
                    conversation = get_conversation_by_conversation_id(conversation_id)
                    if conversation:
                        if conversation.from_end_user_id:
                            from_end_user_id = str(conversation.from_end_user_id)
                        else:
                            raise NotFound("from_end_user_id on conversation Model not found")
                    else:
                        raise NotFound("conversation model is not found")
                
            else:
                conversation_id = args["conversation_id"]
                from_end_user_id=None
                conversation = get_conversation_by_conversation_id(conversation_id)
                if conversation:
                    if conversation.from_end_user_id:
                        from_end_user_id = str(conversation.from_end_user_id)
                    else:
                        raise NotFound("from_end_user_id on conversation Model not found")
                else:
                    raise NotFound("conversation model is not found")

            write_name = None
            if 'auth_nie_dify' in result_dict:
                if result_dict['auth_nie_dify']:
                    
                    NIE_API_TOKEN = os.environ.get("NIE_API_TOKEN")
                    # Decode the token
                    try:
                        decoded_payload = jwt.decode(result_dict['auth_nie_dify'], NIE_API_TOKEN, algorithms=["HS256"])
                        print(decoded_payload)
                    except jwt.ExpiredSignatureError:
                        raise NotFound("Token has expired")
                    except jwt.InvalidTokenError:
                        raise NotFound("Invalid token")
                    
                    write_name = decoded_payload['name']
            
            if write_name:
                modify_data('end_users', from_end_user_id, {"name":write_name}, mode="update")

            return self.check_jsonify(
                {
                    "data": {
                        'conversation_id': conversation_id,
                        'workflow_run_id': workflow_run_id,
                        'message_id': message_id,
                        'chat_type':chat_type,
                        'from_end_user_id':from_end_user_id,
                        'write_name':write_name
                    },
                    "success": True,
                    "message": "End user modified sucessfully",
                    "status": 200,
                }
            ), 200

        except Exception as e:
            return self.handle_error(e)